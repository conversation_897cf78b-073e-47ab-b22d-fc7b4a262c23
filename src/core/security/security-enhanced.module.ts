import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ThrottlerModule } from '@nestjs/throttler';

import { EncryptionService } from './encryption.service';
import { RbacService } from './rbac.service';
import { SessionService } from './session.service';
import { UserService } from './user.service';

import { DatabaseModule } from '../database/database.module';

@Global()
@Module({
  imports: [
    ConfigModule,
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '24h'),
          issuer: configService.get<string>('JWT_ISSUER', 'discord-bot-energex'),
          audience: configService.get<string>('JWT_AUDIENCE', 'discord-bot-energex'),
        },
      }),
      inject: [ConfigService],
    }),
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        throttlers: [
          {
            ttl: configService.get<number>('RATE_LIMIT_TTL', 60) * 1000, // Convert to milliseconds
            limit: configService.get<number>('RATE_LIMIT_MAX', 100),
          },
        ],
      }),
      inject: [ConfigService],
    }),
    DatabaseModule,
  ],
  providers: [
    RbacService,
    EncryptionService,
    SessionService,
    UserService,
  ],
  exports: [
    RbacService,
    EncryptionService,
    SessionService,
    UserService,
  ],
})
export class SecurityEnhancedModule {}
