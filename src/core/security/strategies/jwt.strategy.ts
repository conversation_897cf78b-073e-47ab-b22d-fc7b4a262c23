import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { Request } from 'express';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { AuditService } from '../audit.service';
import { RbacService } from '../rbac.service';
import { SecurityEventService } from '../security-event.service';
import { UserService } from '../user.service';

export interface JwtPayload {
  sub: string; // User ID
  email?: string;
  username?: string;
  organizationId?: string;
  roles?: string[];
  permissions?: string[];
  iat?: number;
  exp?: number;
  iss?: string;
  aud?: string;
  sessionId?: string;
  tokenType?: 'access' | 'refresh';
}

export interface ValidatedUser {
  id: string;
  email?: string;
  username?: string;
  organizationId?: string;
  roles: string[];
  permissions: string[];
  isActive: boolean;
  lastLoginAt?: Date;
  sessionId?: string;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UserService,
    private readonly rbacService: RbacService,
    private readonly securityEventService: SecurityEventService,
    private readonly auditService: AuditService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        JwtStrategy.extractJwtFromCookie,
        JwtStrategy.extractJwtFromAuthHeaderAsBearerToken,
        JwtStrategy.extractJwtFromQuery,
      ]),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET'),
      issuer: configService.get<string>('JWT_ISSUER'),
      audience: configService.get<string>('JWT_AUDIENCE'),
      passReqToCallback: true,
    });
  }

  /**
   * Validate JWT payload and return user
   */
  async validate(request: Request, payload: JwtPayload): Promise<ValidatedUser> {
    try {
      this.logger.debug(`Validating JWT for user: ${payload.sub}`);

      // Basic payload validation
      if (!payload.sub) {
        await this.logAuthenticationFailure(request, 'Missing user ID in JWT payload');
        throw new UnauthorizedException('Invalid token payload');
      }

      // Check token type (only allow access tokens for API access)
      if (payload.tokenType && payload.tokenType !== 'access') {
        await this.logAuthenticationFailure(request, 'Invalid token type', payload.sub);
        throw new UnauthorizedException('Invalid token type');
      }

      // Get user from database
      const user = await this.userService.findById(payload.sub);
      if (!user) {
        await this.logAuthenticationFailure(request, 'User not found', payload.sub);
        throw new UnauthorizedException('User not found');
      }

      // Check if user is active
      if (!user.isActive) {
        await this.logAuthenticationFailure(request, 'User account is inactive', payload.sub);
        throw new UnauthorizedException('Account is inactive');
      }

      // Validate session if sessionId is provided
      if (payload.sessionId) {
        const isValidSession = await this.userService.validateSession(payload.sessionId);
        if (!isValidSession) {
          await this.logAuthenticationFailure(request, 'Invalid session', payload.sub);
          throw new UnauthorizedException('Invalid session');
        }
      }

      // Get fresh roles and permissions from database
      const roles = await this.rbacService.getUserRoles(payload.sub, payload.organizationId);
      const permissions = await this.rbacService.getUserPermissionObjects(payload.sub, payload.organizationId);

      // Create validated user object
      const validatedUser: ValidatedUser = {
        id: payload.sub,
        email: user.email || payload.email,
        username: user.username || payload.username,
        organizationId: payload.organizationId,
        roles: roles.map(role => role.name),
        permissions: permissions.map(permission => permission.name),
        isActive: user.isActive,
        lastLoginAt: user.lastActivityAt,
        sessionId: payload.sessionId,
      };

      // Log successful authentication
      await this.auditService.logEvent(
        'authentication',
        'jwt_validation',
        'user',
        {
          tokenType: payload.tokenType || 'access',
          organizationId: payload.organizationId,
          sessionId: payload.sessionId,
          endpoint: request.url,
          method: request.method,
        },
        {
          userId: payload.sub,
          organizationId: payload.organizationId,
          ipAddress: this.getClientIp(request),
          userAgent: request.get('User-Agent'),
          sessionId: payload.sessionId,
        },
        true,
      );

      // Update last activity
      await this.userService.updateLastActivity(payload.sub);

      this.logger.debug(`JWT validation successful for user: ${payload.sub}`);
      return validatedUser;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }

      this.logger.error('JWT validation error:', error);
      await this.logAuthenticationFailure(request, 'JWT validation error', payload?.sub);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Extract JWT from cookie
   */
  private static extractJwtFromCookie(request: Request): string | null {
    if (request.cookies && request.cookies['access_token']) {
      return request.cookies['access_token'];
    }
    return null;
  }

  /**
   * Extract JWT from Authorization header as Bearer token
   */
  private static extractJwtFromAuthHeaderAsBearerToken(request: Request): string | null {
    return ExtractJwt.fromAuthHeaderAsBearerToken()(request);
  }

  /**
   * Extract JWT from query parameter
   */
  private static extractJwtFromQuery(request: Request): string | null {
    if (request.query && request.query.token && typeof request.query.token === 'string') {
      return request.query.token;
    }
    return null;
  }

  /**
   * Get client IP address
   */
  private getClientIp(request: Request): string {
    return (
      request.get('X-Forwarded-For')?.split(',')[0]?.trim() ||
      request.get('X-Real-IP') ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }

  /**
   * Log authentication failure
   */
  private async logAuthenticationFailure(
    request: Request,
    reason: string,
    userId?: string,
  ): Promise<void> {
    try {
      const ipAddress = this.getClientIp(request);
      const userAgent = request.get('User-Agent');

      // Log security event
      await this.securityEventService.recordFailedLogin(
        userId || 'unknown',
        ipAddress,
        userAgent,
        {
          reason,
          endpoint: request.url,
          method: request.method,
          authMethod: 'jwt',
        },
      );

      // Log audit event
      await this.auditService.logEvent(
        'authentication',
        'jwt_validation_failed',
        'user',
        {
          reason,
          endpoint: request.url,
          method: request.method,
          authMethod: 'jwt',
        },
        {
          userId: userId || 'unknown',
          ipAddress,
          userAgent,
        },
        false,
        reason,
      );
    } catch (error) {
      this.logger.error('Failed to log authentication failure:', error);
    }
  }
}

/**
 * JWT Strategy configuration options
 */
export interface JwtStrategyOptions {
  secretOrKey?: string;
  issuer?: string;
  audience?: string;
  ignoreExpiration?: boolean;
  clockTolerance?: number;
}

/**
 * Create JWT strategy with custom options
 */
export function createJwtStrategy(options: JwtStrategyOptions = {}) {
  return class CustomJwtStrategy extends JwtStrategy {
    constructor(
      configService: ConfigService,
      userService: UserService,
      rbacService: RbacService,
      securityEventService: SecurityEventService,
      auditService: AuditService,
    ) {
      super(configService, userService, rbacService, securityEventService, auditService);
      
      // Override default options
      if (options.secretOrKey) {
        (this as any).secretOrKey = options.secretOrKey;
      }
      if (options.issuer) {
        (this as any).issuer = options.issuer;
      }
      if (options.audience) {
        (this as any).audience = options.audience;
      }
      if (options.ignoreExpiration !== undefined) {
        (this as any).ignoreExpiration = options.ignoreExpiration;
      }
      if (options.clockTolerance) {
        (this as any).clockTolerance = options.clockTolerance;
      }
    }
  };
}
